#!/bin/bash

echo "🚀 启动 Pecco Pet Shop 容器..."

# 启动MySQL服务
echo "📦 启动MySQL服务..."
service mysql start

# 等待MySQL启动
sleep 5

# 创建数据库和用户
echo "🗄️ 初始化数据库..."
mysql -u root << EOF
CREATE DATABASE IF NOT EXISTS pecco DEFAULT CHARSET utf8mb4;
CREATE USER IF NOT EXISTS 'pecco_user'@'localhost' IDENTIFIED BY 'pecco_password';
GRANT ALL PRIVILEGES ON pecco.* TO 'pecco_user'@'localhost';
FLUSH PRIVILEGES;
EOF

# 进入Django项目目录
cd pecco_backend

# 运行数据库迁移
echo "🔄 运行数据库迁移..."
python manage.py makemigrations
python manage.py migrate

# 创建超级用户（如果不存在）
echo "👤 创建管理员用户..."
python manage.py shell << EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123456')
    print('管理员用户已创建: admin/admin123456')
else:
    print('管理员用户已存在')
EOF

# 收集静态文件
echo "📁 收集静态文件..."
python manage.py collectstatic --noinput

echo "✅ 初始化完成，启动Django服务器..."

# 启动Django开发服务器
python manage.py runserver 0.0.0.0:80
