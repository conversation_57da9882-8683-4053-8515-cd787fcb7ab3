FROM python:3.11-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    mysql-server \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制并安装Python依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制项目文件
COPY . .

# 复制启动脚本
COPY docker-start.sh /docker-start.sh
RUN chmod +x /docker-start.sh

# 暴露端口
EXPOSE 80

# 启动命令
CMD ["/docker-start.sh"]
