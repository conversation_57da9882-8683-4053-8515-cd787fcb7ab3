# pecco 轻奢宠物用品官网（Django 原型）

本仓库提供一个基于 Django 的可运行原型：
- MySQL 本地数据库（root/test123456）
- 本地文件系统存储媒体
- 后台管理可维护：轮播、分类、产品、品牌故事、评价、导航、首页布局
- 多语言内容（中英）
- 联系我们表单（默认打印到控制台，可改真实邮箱），提交后展示成功提示
- 管理后台富文本（Post 内容使用 TinyMCE，CDN 方式加载）

## 本地运行步骤

1. 准备 Python 3.11+，安装依赖
```
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

2. 创建数据库（MySQL）
```
mysql -uroot -ptest123456 -e "CREATE DATABASE IF NOT EXISTS pecco DEFAULT CHARSET utf8mb4;"
```

3. 拷贝环境变量
```
cp pecco_backend/.env.example pecco_backend/.env
```

4. 迁移并启动
```
cd pecco_backend
python manage.py makemigrations
python manage.py migrate
python manage.py createsuperuser  # 创建管理员
python manage.py runserver 0.0.0.0:80

```

5. 访问
- 官网：http://127.0.0.1:8000/
- 后台：http://127.0.0.1:8000/admin/

