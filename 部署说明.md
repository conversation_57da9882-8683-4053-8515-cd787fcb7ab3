# Pecco Pet Shop 简单部署指南

## 🚀 快速部署步骤

### 1. 服务器准备
确保你的腾讯云服务器已安装Docker：
```bash
# 安装Docker
curl -fsSL https://get.docker.com | sh
sudo usermod -aG docker $USER
# 重新登录生效
```

### 2. 上传项目
将项目文件上传到服务器：
```bash
scp -r pecco-pet-shop/ user@your-server-ip:/home/<USER>/
```

### 3. 部署运行
SSH登录服务器并运行：
```bash
ssh user@your-server-ip
cd pecco-pet-shop
./start.sh
```

### 4. 访问网站
- 前台：http://你的服务器IP
- 后台：http://你的服务器IP/admin/
- 默认管理员：admin / admin123456

## 📋 注意事项

1. **数据库**：容器内自带MySQL，自动初始化
2. **端口**：使用80端口，确保防火墙已开放
3. **域名**：如有域名，修改DNS解析到服务器IP
4. **首次启动**：需要等待1-2分钟进行数据库初始化

## 🔧 管理命令

```bash
# 查看运行状态
docker ps

# 查看日志
docker logs -f pecco-app

# 停止服务
docker stop pecco-app

# 重启服务
docker restart pecco-app

# 重新部署
./start.sh
```

就这么简单！🎉
