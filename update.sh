#!/bin/bash

echo "🔄 更新 Pecco Pet Shop 代码..."

# 检查容器是否运行
if [ "$(docker ps -q -f name=pecco-app)" ]; then
    echo "📦 停止运行中的容器..."
    docker stop pecco-app
fi

# 备份数据（如果需要）
if [ "$(docker ps -aq -f name=pecco-app)" ]; then
    echo "💾 备份数据库..."
    docker exec pecco-app mysqldump -u pecco_user -ppecco_password pecco > backup_$(date +%Y%m%d_%H%M%S).sql
    echo "✅ 数据库已备份"
fi

# 删除旧容器（保留数据卷）
echo "🗑️ 删除旧容器..."
docker rm pecco-app 2>/dev/null || true

# 重新构建镜像
echo "🔨 重新构建镜像..."
docker build -t pecco-pet-shop .

# 启动新容器（使用相同的数据卷）
echo "🚀 启动新容器..."
docker run -d \
  --name pecco-app \
  -p 80:80 \
  -v pecco_mysql_data:/var/lib/mysql \
  -v pecco_media_data:/app/pecco_backend/media \
  -e MYSQL_HOST=localhost \
  -e MYSQL_DATABASE=pecco \
  -e MYSQL_USER=pecco_user \
  -e MYSQL_PASSWORD=pecco_password \
  -e SECRET_KEY=django-secret-key-12345 \
  -e DEBUG=False \
  pecco-pet-shop

echo "✅ 更新完成！"
echo "🌐 访问地址: http://$(curl -s ifconfig.me || echo 'YOUR_SERVER_IP')"

echo ""
echo "📋 说明:"
echo "  - 数据库数据已保留"
echo "  - 媒体文件已保留"
echo "  - 如有数据库结构变更，会自动运行迁移"
