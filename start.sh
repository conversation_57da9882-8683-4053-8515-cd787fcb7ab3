#!/bin/bash

# 简单的Docker启动脚本

echo "🚀 启动 Pecco Pet Shop..."

# 停止并删除旧容器
docker stop pecco-app 2>/dev/null || true
docker rm pecco-app 2>/dev/null || true

# 构建镜像
echo "📦 构建Docker镜像..."
docker build -t pecco-pet-shop .

# 启动容器
echo "🔄 启动容器..."
docker run -d \
  --name pecco-app \
  -p 80:80 \
  -e MYSQL_HOST=localhost \
  -e MYSQL_DATABASE=pecco \
  -e MYSQL_USER=pecco_user \
  -e MYSQL_PASSWORD=pecco_password \
  -e SECRET_KEY=django-secret-key-$(date +%s) \
  -e DEBUG=False \
  pecco-pet-shop

echo "✅ 启动完成！"
echo "🌐 访问地址: http://$(curl -s ifconfig.me || echo 'YOUR_SERVER_IP')"
echo "🔧 管理后台: http://$(curl -s ifconfig.me || echo 'YOUR_SERVER_IP')/admin/"

echo ""
echo "📋 常用命令:"
echo "  查看日志: docker logs -f pecco-app"
echo "  停止服务: docker stop pecco-app"
echo "  重启服务: docker restart pecco-app"
